# BigQuery Auto-Creation Feature

This document describes the automatic dataset and table creation functionality implemented in the ETL pipeline.

## Overview

The ETL pipeline now automatically creates BigQuery datasets and tables if they don't exist when processing data. This eliminates the need for manual setup and allows for dynamic dataset creation based on company IDs or other parameters.

## Features

### 1. Automatic Dataset Creation
- Datasets are automatically created if they don't exist
- Support for dynamic dataset naming based on company ID
- Configurable location (defaults to "US")
- Proper error handling and logging

### 2. Automatic Table Creation
- Tables are automatically created with the correct schema
- Support for both staging and production tables
- Handles complex schemas with REPEATED fields
- Idempotent operations (safe to call multiple times)

### 3. Dynamic Dataset Naming
- Default: `dobybot.report_api_tester2`
- With company ID: `dobybot_company_{company_id}`
- Customizable base name

## New Functions

### `ensure_dataset_exists(client, dataset_id, location="US")`
Ensures a BigQuery dataset exists, creating it if necessary.

**Parameters:**
- `client`: BigQuery client instance
- `dataset_id`: Full dataset ID (e.g., 'project.dataset')
- `location`: Dataset location (default: 'US')

**Returns:** Dataset object

### `ensure_table_exists(client, table_id, schema)`
Ensures a BigQuery table exists, creating it if necessary.

**Parameters:**
- `client`: BigQuery client instance
- `table_id`: Full table ID (e.g., 'project.dataset.table')
- `schema`: BigQuery schema for the table

**Returns:** Table object

### `get_dataset_name_for_company(company_id=None, base_name="dobybot")`
Generates dataset name based on company ID or returns default.

**Parameters:**
- `company_id`: Company ID (optional)
- `base_name`: Base dataset name (default: 'dobybot')

**Returns:** Dataset name string

## Usage

### API Request
The existing API endpoint now supports automatic creation:

```python
POST /api/etl/bigquery-import/
{
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-01-31T23:59:59Z",
    "company": 123  # Optional: creates company-specific dataset
}
```

### Response
Success response now includes the dataset name:

```json
{
    "status": "success",
    "message": "Loaded 100 orders & 250 items with schema enforcement.",
    "dataset": "dobybot_company_123"
}
```

Error response for setup failures:

```json
{
    "status": "error",
    "message": "Failed to setup BigQuery resources: [error details]"
}
```

## Implementation Details

### Dataset Creation Process
1. Check if dataset exists using `client.get_dataset()`
2. If not found, create new dataset with:
   - Specified location
   - Auto-generated description
   - 30-second timeout

### Table Creation Process
1. Check if table exists using `client.get_table()`
2. If not found, create new table with:
   - Provided schema (including REPEATED fields)
   - Auto-generated description
   - 30-second timeout

### Error Handling
- Comprehensive exception handling for BigQuery operations
- Detailed logging for debugging
- Graceful degradation with meaningful error messages
- HTTP 500 responses for setup failures

## Testing

Run the test script to verify functionality:

```bash
cd etl/
python test_auto_creation.py
```

The test script will:
1. Test dataset creation with company ID
2. Test table creation for orders and order_items
3. Verify idempotent behavior
4. Optionally clean up test resources

## Migration Notes

### Existing Deployments
- No breaking changes to existing API
- Existing datasets and tables remain unchanged
- New functionality is additive

### Configuration
- No additional configuration required
- Uses existing BigQuery client setup
- Inherits project ID from environment

## Best Practices

1. **Company-Specific Datasets**: Use company ID parameter for multi-tenant scenarios
2. **Error Monitoring**: Monitor logs for dataset/table creation events
3. **Permissions**: Ensure BigQuery service account has dataset creation permissions
4. **Testing**: Test with new company IDs in development environment first

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure service account has `bigquery.datasets.create` permission
2. **Location Conflicts**: Ensure consistent location settings across datasets
3. **Quota Limits**: Monitor BigQuery quotas for dataset creation

### Logging
All operations are logged with appropriate levels:
- `INFO`: Successful operations
- `ERROR`: Failed operations with details

Check application logs for detailed information about dataset and table creation activities.
