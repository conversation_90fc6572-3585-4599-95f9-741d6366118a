#!/usr/bin/env python3
"""
Test script for BigQuery auto-creation functionality.
This script tests the new dataset and table auto-creation features.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from google.cloud import bigquery
from etl.views import (
    ensure_dataset_exists,
    ensure_table_exists,
    get_dataset_name_for_company,
    ORDERS_SCHEMA,
    ORDER_ITEMS_SCHEMA
)


def test_dataset_creation():
    """Test dataset creation functionality."""
    print("Testing dataset creation...")
    
    client = bigquery.Client()
    
    # Test with company ID
    dataset_name = get_dataset_name_for_company(company_id=999)
    print(f"Generated dataset name for company 999: {dataset_name}")
    
    try:
        # This should create the dataset if it doesn't exist
        dataset = ensure_dataset_exists(client, dataset_name)
        print(f"✓ Dataset {dataset_name} ensured successfully")
        
        # Test table creation
        test_table_id = f"{dataset_name}.test_orders"
        table = ensure_table_exists(client, test_table_id, ORDERS_SCHEMA)
        print(f"✓ Table {test_table_id} ensured successfully")
        
        test_items_table_id = f"{dataset_name}.test_order_items"
        items_table = ensure_table_exists(client, test_items_table_id, ORDER_ITEMS_SCHEMA)
        print(f"✓ Table {test_items_table_id} ensured successfully")
        
        # Test that subsequent calls don't fail
        dataset2 = ensure_dataset_exists(client, dataset_name)
        table2 = ensure_table_exists(client, test_table_id, ORDERS_SCHEMA)
        print("✓ Subsequent calls to ensure functions work correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        return False


def test_default_dataset():
    """Test default dataset functionality."""
    print("\nTesting default dataset...")
    
    # Test without company ID
    dataset_name = get_dataset_name_for_company()
    print(f"Generated default dataset name: {dataset_name}")
    
    return True


def cleanup_test_resources():
    """Clean up test resources (optional)."""
    print("\nCleaning up test resources...")
    
    client = bigquery.Client()
    dataset_name = get_dataset_name_for_company(company_id=999)
    
    try:
        # Delete test tables
        test_tables = [
            f"{dataset_name}.test_orders",
            f"{dataset_name}.test_order_items"
        ]
        
        for table_id in test_tables:
            try:
                client.delete_table(table_id)
                print(f"✓ Deleted table {table_id}")
            except Exception as e:
                print(f"- Table {table_id} not found or already deleted: {e}")
        
        # Delete test dataset
        try:
            client.delete_dataset(dataset_name, delete_contents=True)
            print(f"✓ Deleted dataset {dataset_name}")
        except Exception as e:
            print(f"- Dataset {dataset_name} not found or already deleted: {e}")
            
    except Exception as e:
        print(f"Error during cleanup: {e}")


if __name__ == "__main__":
    print("BigQuery Auto-Creation Test Script")
    print("=" * 40)
    
    # Run tests
    success = True
    
    success &= test_default_dataset()
    success &= test_dataset_creation()
    
    if success:
        print("\n✓ All tests passed!")
        
        # Ask user if they want to clean up
        cleanup = input("\nDo you want to clean up test resources? (y/N): ").lower().strip()
        if cleanup in ['y', 'yes']:
            cleanup_test_resources()
        else:
            print("Test resources left in place for manual inspection.")
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)
